#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test simple de l'orchestrateur synchrone.
"""

import sys
sys.path.insert(0, '.')

from pydantic import SecretStr
from src.kbotloadscheduler.loader.confluence.orchestrator import SyncOrchestrator
from src.kbotloadscheduler.loader.confluence.config import (
    ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig
)

def test_orchestrator_creation():
    """Test de création de l'orchestrateur synchrone."""
    print("Test de création de l'orchestrateur synchrone...")
    
    # Configuration
    config = ConfluenceConfig(
        url="https://example.atlassian.net",
        pat_token=SecretStr("test_pat_token_123456789"),
        default_space_key="TEST"
    )
    
    criteria = SearchCriteria(
        spaces=["TEST"],
        max_results=5
    )
    
    storage_config = StorageConfig(
        type="filesystem",
        base_dir="test_output"
    )
    
    processing_config = ProcessingConfig(
        chunk_size=1000,
        overlap_size=200,
        max_parallel_downloads=3,
        max_thread_workers=2
    )
    
    try:
        orchestrator = SyncOrchestrator(
            config=config,
            criteria=criteria,
            storage_config=storage_config,
            processing_config=processing_config
        )
        
        print(f"✓ Orchestrateur créé avec succès")
        print(f"  - Client: {type(orchestrator.client).__name__}")
        print(f"  - Content Retriever: {type(orchestrator.content_retriever).__name__}")
        print(f"  - Storage type: {orchestrator.storage_config.type}")
        print(f"  - Max parallel downloads: {orchestrator.processing_config.max_parallel_downloads}")
        print(f"  - Thread pool manager: {type(orchestrator.thread_pool_manager).__name__}")
        
        # Test des statistiques initiales
        print(f"  - Stats initiales: {orchestrator.stats}")
        
        # Test du statut de synchronisation
        status = orchestrator.get_sync_status()
        print(f"  - Statut sync: {status}")
        
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors de la création de l'orchestrateur: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_orchestrator_components():
    """Test des composants de l'orchestrateur."""
    print("\nTest des composants de l'orchestrateur...")
    
    config = ConfluenceConfig(
        url="https://example.atlassian.net",
        pat_token=SecretStr("test_pat_token_123456789"),
        default_space_key="TEST"
    )
    
    criteria = SearchCriteria(spaces=["TEST"], max_results=5)
    
    try:
        orchestrator = SyncOrchestrator(config, criteria)
        
        # Vérifier que tous les composants sont synchrones
        print(f"✓ Client synchrone: {hasattr(orchestrator.client, 'search_content')}")
        print(f"✓ Content retriever synchrone: {hasattr(orchestrator.content_retriever, 'search_and_retrieve')}")
        print(f"✓ Thread pool manager: {orchestrator.thread_pool_manager is not None}")
        
        # Vérifier les méthodes de l'orchestrateur
        methods = ['run', '_retrieve_content', '_process_changed_content', 'get_sync_status']
        for method in methods:
            has_method = hasattr(orchestrator, method)
            print(f"✓ Méthode {method}: {has_method}")
        
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors du test des composants: {e}")
        return False

def test_configuration_validation():
    """Test de validation de la configuration."""
    print("\nTest de validation de la configuration...")
    
    try:
        # Test avec configuration minimale
        config = ConfluenceConfig(
            url="https://example.atlassian.net",
            pat_token=SecretStr("test_token")
        )
        
        criteria = SearchCriteria()
        
        orchestrator = SyncOrchestrator(config, criteria)
        print(f"✓ Configuration minimale acceptée")
        
        # Test avec configuration complète
        storage_config = StorageConfig(
            type="filesystem",
            base_dir="test_output",
            include_attachments=True,
            max_attachment_size_mb=10
        )
        
        processing_config = ProcessingConfig(
            chunk_size=500,
            overlap_size=100,
            max_parallel_downloads=2,
            max_thread_workers=3
        )
        
        orchestrator2 = SyncOrchestrator(
            config, criteria, storage_config, processing_config
        )
        print(f"✓ Configuration complète acceptée")
        
        return True
        
    except Exception as e:
        print(f"✗ Erreur lors de la validation: {e}")
        return False

if __name__ == "__main__":
    print("=== Test de l'orchestrateur Confluence synchrone ===")
    
    success = True
    success &= test_orchestrator_creation()
    success &= test_orchestrator_components()
    success &= test_configuration_validation()
    
    if success:
        print("\n🎉 Tous les tests sont passés!")
        print("\n📚 L'orchestrateur synchrone est prêt à être utilisé!")
        print("   - Utilise le client Confluence synchrone")
        print("   - Gère le parallélisme avec ThreadPoolExecutor")
        print("   - Compatible avec la configuration existante")
        print("   - Méthodes synchrones pour l'intégration")
    else:
        print("\n❌ Certains tests ont échoué")
        sys.exit(1)
