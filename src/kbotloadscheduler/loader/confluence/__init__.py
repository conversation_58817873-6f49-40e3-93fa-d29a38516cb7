#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Système RAG Confluence avec optimisations de gestion des threads.
"""

from .config import (
    ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig,
    ThreadPoolConfig, RetryConfig, CircuitBreakerConfig, LoggingConfig, HealthCheckConfig
)
from .client import ConfluenceClient
from .sync_client import SyncConfluenceClient
from .processing import AttachmentProcessor, ContentRetriever
from .processing.sync_content_retriever import SyncContentRetriever
from .processing.sync_attachment_processor import SyncAttachmentProcessor
from .storage import FileSystemStorage, GCSStorage, get_storage_provider
from .orchestrator import SyncOrchestrator
from .thread_pool_manager import (
    ThreadPoolManager, get_thread_pool_manager, shutdown_global_thread_pools,
    thread_pool_executor
)
from .health_check import <PERSON>Checker, HealthStatus, HealthCheckResult, SystemHealthReport

__version__ = "1.2.0"
__author__ = "Profiling Team"
__description__ = "Client pour la récupération de documents sur Confluence avec optimisations de gestion des threads"

__all__ = [
    # Configuration
    "ConfluenceConfig",
    "SearchCriteria",
    "StorageConfig",
    "ProcessingConfig",
    "ThreadPoolConfig",
    "RetryConfig",
    "CircuitBreakerConfig",
    "LoggingConfig",
    "HealthCheckConfig",

    # Client et traitement
    "ConfluenceClient",
    "SyncConfluenceClient",
    "AttachmentProcessor",
    "ContentRetriever",
    "SyncContentRetriever",
    "SyncAttachmentProcessor",

    # Stockage
    "FileSystemStorage",
    "GCSStorage",
    "get_storage_provider",

    # Orchestration
    "SyncOrchestrator",

    # Gestion des threads optimisée
    "ThreadPoolManager",
    "get_thread_pool_manager",
    "shutdown_global_thread_pools",
    "thread_pool_executor",

    # Health checks
    "HealthChecker",
    "HealthStatus",
    "HealthCheckResult",
    "SystemHealthReport",
]