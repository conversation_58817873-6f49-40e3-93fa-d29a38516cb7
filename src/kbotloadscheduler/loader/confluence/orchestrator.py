#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Orchestration du processus de synchronisation pour le système RAG Confluence.
"""

import os
import logging
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Any, Optional
from datetime import datetime

from .config import ConfluenceConfig, SearchCriteria, StorageConfig, ProcessingConfig
from .sync_client import SyncConfluenceClient
from .processing.sync_content_retriever import SyncContentRetriever
from .tracking import ConfluenceChangeTracker
from .storage import get_storage_provider, StorageProvider
from .models import ContentItem, AttachmentDetail
from .thread_pool_manager import get_thread_pool_manager
from .exceptions import (
    ConfluenceRAGException, AuthenticationError, APIError,
    RateLimitExceededError, ContentProcessingError
)
from .logging_utils import CorrelationContext, propagate_correlation_id_sync


class SyncOrchestrator:
    """Orchestrateur du processus de synchronisation."""

    def __init__(
            self,
            config: ConfluenceConfig,
            criteria: SearchCriteria,
            storage_config: StorageConfig = None,
            processing_config: ProcessingConfig = None
    ):
        """
        Initialise l'orchestrateur avec la configuration et les critères de recherche.

        Args:
            config: Configuration Confluence
            criteria: Critères de recherche
            storage_config: Configuration de stockage (optionnelle)
            processing_config: Configuration de traitement (optionnelle)
        """
        self.config = config
        self.criteria = criteria
        self.storage_config = storage_config or StorageConfig()
        self.processing_config = processing_config or ProcessingConfig.from_env()
        self.client = SyncConfluenceClient(config)
        self.content_retriever = SyncContentRetriever(self.client, self.processing_config, self.storage_config)
        self.change_tracker = ConfluenceChangeTracker()
        self.logger = logging.getLogger(__name__)

        # Thread pool manager for parallel processing
        self.thread_pool_manager = get_thread_pool_manager(self.processing_config.thread_pool_config)

        # Initialiser le fournisseur de stockage
        if self.storage_config.type == "filesystem":
            self.storage = get_storage_provider("filesystem", base_dir=self.storage_config.base_dir)
        elif self.storage_config.type == "gcs":
            self.storage = get_storage_provider("gcs",
                                                bucket_name=self.storage_config.bucket_name,
                                                base_prefix=self.storage_config.base_prefix)
        else:
            raise ValueError(f"Type de stockage non supporté: {self.storage_config.type}")

        self.logger.info(
            f"SyncOrchestrator initialisé avec max_parallel_downloads={self.processing_config.max_parallel_downloads}, "
            f"max_thread_workers={self.processing_config.max_thread_workers}"
        )

        # Statistiques de synchronisation
        self.stats = {
            "start_time": None,
            "end_time": None,
            "total_content_items": 0,
            "total_attachments": 0,
            "changed_content_items": 0,
            "changed_attachments": 0,
            "stored_content_items": 0,
            "stored_attachments": 0,
            "errors": 0,
            "warnings": 0,
            "storage_type": self.storage_config.type
        }

    @propagate_correlation_id_sync
    def run(self) -> Dict[str, Any]:
        """Exécute le processus de synchronisation complet."""
        self.stats["start_time"] = datetime.now()

        # Récupérer l'identifiant de corrélation actuel
        correlation_id = CorrelationContext.get_correlation_id()
        if correlation_id:
            self.logger.info(f"Début de la synchronisation avec Confluence: {self.config.url} [correlation_id={correlation_id}]")
            # S'assurer que l'identifiant de corrélation est dans les statistiques
            self.stats["correlation_id"] = correlation_id
        else:
            self.logger.info(f"Début de la synchronisation avec Confluence: {self.config.url} [sans identifiant de corrélation]")

        try:
            # Rechercher et récupérer les contenus
            content_items = self._retrieve_content()

            # Traiter les contenus modifiés
            changed_items = self._process_changed_content(content_items)

            # Enregistrer la synchronisation
            sync_info = self.change_tracker.record_sync(content_items)

            # Mettre à jour les statistiques
            self.stats["end_time"] = datetime.now()
            self.stats["total_content_items"] = len(content_items)
            self.stats["total_attachments"] = sum(len(item.attachments) for item in content_items)
            self.stats["changed_content_items"] = len(changed_items)
            self.stats["processing_time_seconds"] = (
                    self.stats["end_time"] - self.stats["start_time"]
            ).total_seconds()

            self.logger.info(f"Fin de la synchronisation. Statistiques: {self.stats}")

            return {**self.stats, **sync_info}
        except AuthenticationError as e:
            self.logger.error(f"Erreur d'authentification: {e}")
            self.stats["errors"] += 1
            raise
        except RateLimitExceededError as e:
            self.logger.error(f"Limite de taux d'appels API dépassée: {e}")
            self.stats["errors"] += 1
            raise
        except APIError as e:
            self.logger.error(f"Erreur API: {e}")
            self.stats["errors"] += 1
            raise
        except Exception as e:
            self.logger.error(f"Erreur inattendue lors de la synchronisation: {e}")
            self.stats["errors"] += 1
            raise

    @propagate_correlation_id_sync
    def _retrieve_content(self) -> List[ContentItem]:
        """Recherche et récupère les contenus selon les critères spécifiés."""
        try:
            self.logger.info(f"Recherche de contenu avec les critères: {self.criteria}")

            # Rechercher et récupérer les contenus
            content_items = self.content_retriever.search_and_retrieve(
                self.criteria,
                process_attachments=self.criteria.include_attachments
            )

            self.logger.info(f"Récupération de {len(content_items)} éléments de contenu terminée")

            return content_items
        except Exception as e:
            self.logger.error(f"Erreur lors de la récupération du contenu: {e}")
            self.stats["errors"] += 1
            raise

    @propagate_correlation_id_sync
    def _process_changed_content(self, content_items: List[ContentItem]) -> List[ContentItem]:
        """Traite les contenus qui ont changé depuis la dernière synchronisation."""
        changed_items = []

        for item in content_items:
            try:
                # Vérifier si le contenu a changé
                if self.change_tracker.has_content_changed(item):
                    self.logger.info(f"Contenu modifié détecté: {item.id} - {item.title}")
                    changed_items.append(item)
                    self.stats["changed_content_items"] += 1

                    # Stocker le contenu modifié
                    self._store_content(item)

                    # Vérifier les pièces jointes modifiées
                    for attachment in item.attachments:
                        if self.change_tracker.has_attachment_changed(attachment):
                            self.logger.info(
                                f"Pièce jointe modifiée détectée: {attachment.id} - {attachment.file_name}"
                            )
                            self.stats["changed_attachments"] += 1

                            # Stocker la pièce jointe modifiée si elle est incluse dans les critères
                            if self.storage_config.include_attachments:
                                self._store_attachment(item.id, attachment)
            except Exception as e:
                self.logger.error(f"Erreur lors du traitement du contenu {item.id}: {e}")
                self.stats["errors"] += 1
                # Continuer avec le contenu suivant

        self.logger.info(
            f"Traitement terminé. {len(changed_items)} contenus modifiés, "
            f"{self.stats['changed_attachments']} pièces jointes modifiées, "
            f"{self.stats['stored_content_items']} contenus stockés, "
            f"{self.stats['stored_attachments']} pièces jointes stockées."
        )

        return changed_items

    @propagate_correlation_id_sync
    def _store_content(self, content_item: ContentItem) -> str:
        """Stocke un contenu dans le système de stockage configuré."""
        try:
            # Convertir l'objet ContentItem en dictionnaire pour le stockage
            # FIX: Changed .dict() to .model_dump() for Pydantic V2 compatibility
            content_data = content_item.model_dump(exclude={'attachments'})

            # Ajouter des métadonnées supplémentaires
            content_data["storage_timestamp"] = datetime.now().isoformat()
            content_data["storage_type"] = self.storage_config.type

            # Stocker le contenu
            storage_path = self.storage.save_content(content_item.id, content_data)

            self.logger.info(f"Contenu {content_item.id} stocké dans {storage_path}")
            self.stats["stored_content_items"] += 1

            return storage_path
        except Exception as e:
            self.logger.error(f"Erreur lors du stockage du contenu {content_item.id}: {e}")
            self.stats["errors"] += 1
            raise

    @propagate_correlation_id_sync
    def _store_attachment(self, content_id: str, attachment: AttachmentDetail) -> Optional[str]:
        """Stocke une pièce jointe dans le système de stockage configuré."""
        try:
            # Vérifier si l'extension de la pièce jointe est dans les listes d'extensions supportées
            _, ext = os.path.splitext(attachment.file_name.lower())
            should_convert = ext in self.storage_config.attachment_extensions_to_convert
            should_download_raw = ext in self.storage_config.attachment_extensions_to_download_raw

            if not should_convert and not should_download_raw:
                self.logger.info(f"Extension {ext} non incluse dans les extensions supportées, ignorée")
                return None

            # Vérifier la taille de la pièce jointe
            max_size_bytes = self.storage_config.max_attachment_size_mb * 1024 * 1024
            if attachment.file_size > max_size_bytes:
                self.logger.warning(
                    f"Pièce jointe {attachment.id} trop volumineuse "
                    f"({attachment.file_size} octets > {max_size_bytes} octets), ignorée"
                )
                self.stats["warnings"] += 1 # It might be good to count these warnings
                return None

            # Traitement différencié selon le type de fichier
            if should_download_raw:
                # Télécharger et stocker le fichier brut (PDF, XLSX, DOCX, images)
                self.logger.info(f"Téléchargement du fichier brut: {attachment.file_name}")
                attachment_data = self.client.download_attachment(attachment)

                # Stocker la pièce jointe brute
                storage_path = self.storage.save_attachment(
                    content_id,
                    attachment.id,
                    attachment.file_name,
                    attachment_data
                )

                self.logger.info(f"Fichier brut {attachment.id} stocké dans {storage_path}")
                self.stats["stored_attachments"] += 1
                return storage_path

            elif should_convert:
                # Traiter le fichier pour extraction de texte (existant)
                self.logger.info(f"Traitement pour extraction de texte: {attachment.file_name}")
                # Le traitement d'extraction de texte est déjà géré par l'AttachmentProcessor
                # dans le ContentRetriever, donc ici on stocke juste les métadonnées
                # si l'attachment a déjà été traité
                if hasattr(attachment, 'extracted_text') and attachment.extracted_text:
                    # Stocker les métadonnées et le texte extrait
                    attachment_metadata = {
                        "id": attachment.id,
                        "file_name": attachment.file_name,
                        "media_type": attachment.media_type,
                        "file_size": attachment.file_size,
                        "extracted_text": attachment.extracted_text,
                        "processing_status": getattr(attachment, 'processing_status', 'unknown'),
                        "storage_timestamp": datetime.now().isoformat()
                    }

                    storage_path = self.storage.save_content(
                        f"{content_id}_attachment_{attachment.id}",
                        attachment_metadata
                    )

                    self.logger.info(f"Métadonnées d'attachment {attachment.id} stockées dans {storage_path}")
                    self.stats["stored_attachments"] += 1
                    return storage_path
                else:
                    self.logger.warning(f"Attachment {attachment.id} marqué pour conversion mais pas de texte extrait")
                    return None
        except Exception as e:
            self.logger.error(f"Erreur lors du stockage de la pièce jointe {attachment.id}: {e}")
            self.stats["errors"] += 1
            return None # Return None on error to indicate failure but allow process to continue for other attachments

    @propagate_correlation_id
    def get_sync_status(self) -> Dict[str, Any]:
        """Récupère le statut actuel de la synchronisation."""
        # Récupérer les informations de la dernière synchronisation
        last_sync = self.change_tracker.get_last_sync_info()

        status = {
            "is_running": self.stats["start_time"] is not None and self.stats["end_time"] is None,
            "last_sync": last_sync,
            "current_stats": self.stats
        }

        return status